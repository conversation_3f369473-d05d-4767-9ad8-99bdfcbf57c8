# Week 6 Deployment Guide - Graph Service & Authority Calculator

## 🎯 Overview

Week 6 introduces the Graph API with React-Flow compatible JSON output and the Authority Calculator using PageRank with recency boost. This guide covers the complete deployment process including Cloud Scheduler setup for nightly authority jobs.

## 📋 Prerequisites

### 1. Required Tools
```bash
# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Install Docker
# Follow instructions at: https://docs.docker.com/get-docker/

# Verify installations
gcloud --version
docker --version
```

### 2. GCP Setup
```bash
# Authenticate with Google Cloud
gcloud auth login

# Set your project ID
export PROJECT_ID=your-actual-gcp-project-id
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable cloudscheduler.googleapis.com
```

### 3. Environment Variables
```bash
# Required
export PROJECT_ID=your-actual-gcp-project-id

# Optional (defaults provided)
export TAG=week6
export REGION=us-central1
export SERVICE_NAME=legal-api-week6-stg
```

## 🚀 Deployment Steps

### Step 1: Deploy Week 6 API to Cloud Run

```bash
# Navigate to repository root
cd /path/to/texas-laws-personalinjury

# Set your project ID
export PROJECT_ID=your-actual-gcp-project-id

# Run the Week 6 deployment script
./scripts/deploy_week6.sh
```

The script will:
- ✅ Check all prerequisites
- ✅ Build the Docker image with Week 6 features
- ✅ Push to Google Container Registry
- ✅ Deploy to Cloud Run with proper configuration
- ✅ Run smoke tests (health, API root, graph sample, metrics)
- ✅ Provide next steps

### Step 2: Set Up Nightly Authority Job

```bash
# Set up Cloud Scheduler for nightly authority calculation
./scripts/setup_authority_job.sh
```

This will:
- ✅ Enable Cloud Scheduler API
- ✅ Create/update the nightly job (2 AM UTC daily)
- ✅ Configure proper retry policies
- ✅ Optionally trigger a test run

## 🔧 Week 6 Features

### Graph API
- **Endpoint**: `/v0/graph`
- **Format**: React-Flow compatible JSON
- **Limits**: 200 nodes/600 edges (default), 500/1500 (hard cap)
- **Parameters**: depth (1-3), direction (both/in/out), node filtering
- **Caching**: 15-minute TTL with user permissions

### Authority Calculator
- **Algorithm**: PageRank with damping=0.85, max_iter=100, tol=1e-6
- **Recency Boost**: `authority = 0.8 * pagerank + 0.2 * e^(-0.1*age_years)`
- **Schedule**: Daily at 2 AM UTC
- **Storage**: Updates both Neo4j and Supabase
- **Monitoring**: Prometheus metrics

### Jobs API
- **Trigger**: `POST /jobs/authority/calculate`
- **Status**: `GET /jobs/authority/status`
- **Background**: FastAPI background tasks
- **Scheduler**: Cloud Scheduler integration

## 🧪 Testing the Deployment

### 1. Test Graph API
```bash
# Get sample graph data (no auth required)
curl "$SERVICE_URL/v0/graph/sample" | jq

# Test authenticated graph endpoint
curl -H "Authorization: Bearer $JWT_TOKEN" \
     "$SERVICE_URL/v0/graph?id=C-2023-TX-123&depth=2&max_nodes=200" | jq
```

### 2. Test Authority Job
```bash
# Check authority job status
curl "$SERVICE_URL/jobs/authority/status" | jq

# Trigger manual authority calculation (dry run)
curl -X POST "$SERVICE_URL/jobs/authority/calculate" \
     -H "Content-Type: application/json" \
     -d '{"dry_run": true}' | jq
```

### 3. Test Cloud Scheduler
```bash
# Check job status
gcloud scheduler jobs describe authority-nightly --location=$REGION

# Trigger manual run
gcloud scheduler jobs run authority-nightly --location=$REGION

# View logs
gcloud logging read "resource.type=cloud_scheduler_job" --limit=5
```

## 📊 Monitoring

### Prometheus Metrics
The authority calculator exposes these metrics:
- `authority_calculation_age_hours`: Hours since last calculation
- `authority_calculations_total`: Total calculations by status
- `authority_calculation_duration_seconds`: Duration histogram

### Health Checks
```bash
# API health
curl "$SERVICE_URL/health"

# Authority job status
curl "$SERVICE_URL/jobs/authority/status"

# Metrics endpoint
curl "$SERVICE_URL/metrics"
```

### Alerts Setup
Add to your monitoring system:
- **Stale Authority**: Alert if not updated in >26 hours
- **Job Failures**: Alert on authority calculation failures
- **Performance**: Alert if calculation takes >5 minutes

## 🔧 Configuration

### Cloud Run Environment Variables
```bash
CACHE_BACKEND=memory                    # Staging uses memory cache
RATE_LIMIT_BACKEND=memory              # Staging uses memory rate limiting
SUPABASE_JWKS_URL=https://...          # JWT validation
LOG_LEVEL=INFO                         # Logging level
```

### Cloud Scheduler Configuration
```bash
Schedule: "0 2 * * *"                  # Daily at 2 AM UTC
Max Retries: 3                         # Retry failed jobs
Max Duration: 1 hour                   # Timeout for long jobs
Backoff: 60s-300s                      # Exponential backoff
```

## 🚨 Troubleshooting

### Common Issues

1. **Graph API 500 Errors**
   ```bash
   # Check logs
   gcloud run services logs tail legal-api-week6-stg --region=$REGION
   
   # Verify database connections
   curl "$SERVICE_URL/health"
   ```

2. **Authority Job Failures**
   ```bash
   # Check job logs
   gcloud logging read "resource.type=cloud_scheduler_job" --limit=10
   
   # Check authority job status
   curl "$SERVICE_URL/jobs/authority/status"
   ```

3. **Authentication Issues**
   ```bash
   # Verify JWT configuration
   echo $SUPABASE_JWKS_URL
   
   # Test with valid token
   curl -H "Authorization: Bearer $JWT_TOKEN" "$SERVICE_URL/v0/search?q=test"
   ```

## 🎉 Success Criteria

- ✅ Service deploys without errors
- ✅ Health check returns 200
- ✅ Graph API returns React-Flow compatible JSON
- ✅ Authority job endpoint responds correctly
- ✅ Cloud Scheduler job is created and configured
- ✅ Sample endpoints work without authentication
- ✅ Authenticated endpoints work with valid JWT
- ✅ Authority calculation can be triggered manually
- ✅ Metrics endpoint provides monitoring data

## 📞 Support

If you encounter issues:
1. Check the logs: `gcloud run services logs tail legal-api-week6-stg --region=$REGION`
2. Verify environment variables are set correctly
3. Test with the sample endpoints first
4. Ensure JWT tokens are valid and not expired
5. Check Cloud Scheduler job configuration

**Week 6 is now ready for production with Graph API and Authority Calculator!** 🚀
