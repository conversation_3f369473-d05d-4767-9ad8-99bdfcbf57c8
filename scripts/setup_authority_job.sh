#!/bin/bash
# Setup Cloud Scheduler for Nightly Authority Job

set -e  # Exit on any error

# Configuration
PROJECT_ID="${PROJECT_ID:-your-gcp-project}"
REGION="${REGION:-us-central1}"
SERVICE_NAME="${SERVICE_NAME:-legal-api-week6-stg}"
JOB_NAME="authority-nightly"
SCHEDULE="0 2 * * *"  # 2 AM UTC daily
TIME_ZONE="UTC"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🕐 Setting up Nightly Authority Job${NC}"
echo "=================================="

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

if [ -z "$PROJECT_ID" ] || [ "$PROJECT_ID" = "your-gcp-project" ]; then
    echo -e "${RED}❌ Please set PROJECT_ID environment variable${NC}"
    echo "   export PROJECT_ID=your-actual-gcp-project-id"
    exit 1
fi

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI not found. Please install Google Cloud SDK${NC}"
    exit 1
fi

# Check if authenticated with gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not authenticated with gcloud. Please run: gcloud auth login${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Set the project
echo -e "${YELLOW}🔧 Setting GCP project to: $PROJECT_ID${NC}"
gcloud config set project $PROJECT_ID

# Enable Cloud Scheduler API
echo -e "${YELLOW}🔧 Enabling Cloud Scheduler API...${NC}"
gcloud services enable cloudscheduler.googleapis.com

# Get the service URL
echo -e "${YELLOW}🔍 Getting Cloud Run service URL...${NC}"
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format='value(status.url)' 2>/dev/null || echo "")

if [ -z "$SERVICE_URL" ]; then
    echo -e "${RED}❌ Cloud Run service '$SERVICE_NAME' not found in region '$REGION'${NC}"
    echo "   Please deploy the service first using: ./scripts/deploy_week6.sh"
    exit 1
fi

echo -e "${GREEN}✅ Found service: $SERVICE_URL${NC}"

# Check if job already exists
echo -e "${YELLOW}🔍 Checking if authority job already exists...${NC}"
if gcloud scheduler jobs describe $JOB_NAME --location=$REGION &>/dev/null; then
    echo -e "${YELLOW}⚠️  Job '$JOB_NAME' already exists. Updating...${NC}"
    
    # Update existing job
    gcloud scheduler jobs update http $JOB_NAME \
        --location=$REGION \
        --schedule="$SCHEDULE" \
        --uri="$SERVICE_URL/jobs/authority/calculate" \
        --http-method=POST \
        --headers="Content-Type=application/json" \
        --body='{"force": false, "dry_run": false}' \
        --time-zone="$TIME_ZONE" \
        --max-retry-attempts=3 \
        --max-retry-duration=3600s \
        --min-backoff-duration=60s \
        --max-backoff-duration=300s
    
    echo -e "${GREEN}✅ Authority job updated successfully${NC}"
else
    echo -e "${YELLOW}📅 Creating new authority job...${NC}"
    
    # Create new job
    gcloud scheduler jobs create http $JOB_NAME \
        --location=$REGION \
        --schedule="$SCHEDULE" \
        --uri="$SERVICE_URL/jobs/authority/calculate" \
        --http-method=POST \
        --headers="Content-Type=application/json" \
        --body='{"force": false, "dry_run": false}' \
        --time-zone="$TIME_ZONE" \
        --description="Nightly authority score calculation using PageRank with recency boost" \
        --max-retry-attempts=3 \
        --max-retry-duration=3600s \
        --min-backoff-duration=60s \
        --max-backoff-duration=300s
    
    echo -e "${GREEN}✅ Authority job created successfully${NC}"
fi

echo ""
echo -e "${BLUE}📋 Job Configuration${NC}"
echo "===================="
echo "🏷️  Job Name: $JOB_NAME"
echo "📍 Location: $REGION"
echo "⏰ Schedule: $SCHEDULE ($TIME_ZONE)"
echo "🌐 Target URL: $SERVICE_URL/jobs/authority/calculate"
echo "🔄 Max Retries: 3"
echo "⏱️  Max Duration: 1 hour"
echo ""

# Test the job (optional)
echo -e "${YELLOW}🧪 Testing authority job (optional)...${NC}"
read -p "Do you want to trigger a test run now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🚀 Triggering test run...${NC}"
    
    gcloud scheduler jobs run $JOB_NAME --location=$REGION
    
    echo -e "${GREEN}✅ Test run triggered${NC}"
    echo "   Check logs with: gcloud logging read \"resource.type=cloud_scheduler_job\" --limit=5"
else
    echo -e "${BLUE}ℹ️  Skipping test run${NC}"
fi

echo ""
echo -e "${BLUE}🎯 Next Steps${NC}"
echo "============="
echo "1. Monitor job execution:"
echo "   gcloud scheduler jobs describe $JOB_NAME --location=$REGION"
echo ""
echo "2. View job logs:"
echo "   gcloud logging read \"resource.type=cloud_scheduler_job AND resource.labels.job_id=$JOB_NAME\" --limit=10"
echo ""
echo "3. Manually trigger job:"
echo "   gcloud scheduler jobs run $JOB_NAME --location=$REGION"
echo ""
echo "4. Verify authority scores are updated:"
echo "   Check Supabase documents table for authority_score and authority_updated_at fields"
echo ""
echo "5. Set up monitoring alerts (see docs/nightly_authority_job.md)"
echo ""
echo -e "${GREEN}🎉 Nightly authority job setup complete!${NC}"
echo -e "${BLUE}📊 The job will run daily at 2 AM UTC to calculate authority scores${NC}"
