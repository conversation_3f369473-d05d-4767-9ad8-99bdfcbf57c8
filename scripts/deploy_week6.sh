#!/bin/bash
# Week 6 Production Deployment Script - Graph Service & Authority Calculator

set -e  # Exit on any error

# Configuration
TAG="week6"
PROJECT_ID="${PROJECT_ID:-your-gcp-project}"
REGION="${REGION:-us-central1}"
SERVICE_NAME="legal-api-week6-stg"
SUPABASE_JWKS_URL="https://anwefmklplkjxkmzpnva.supabase.co/auth/v1/keys"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Week 6 Production Deployment - Graph Service & Authority Calculator${NC}"
echo "====================================================================="

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

if [ -z "$PROJECT_ID" ] || [ "$PROJECT_ID" = "your-gcp-project" ]; then
    echo -e "${RED}❌ Please set PROJECT_ID environment variable${NC}"
    echo "   export PROJECT_ID=your-actual-gcp-project-id"
    exit 1
fi

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI not found. Please install Google Cloud SDK${NC}"
    exit 1
fi

# Check if docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker not found. Please install Docker${NC}"
    exit 1
fi

# Check if authenticated with gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not authenticated with gcloud. Please run: gcloud auth login${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Set the project
echo -e "${YELLOW}🔧 Setting GCP project to: $PROJECT_ID${NC}"
gcloud config set project $PROJECT_ID

# Enable required APIs
echo -e "${YELLOW}🔧 Enabling required GCP APIs...${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable cloudscheduler.googleapis.com

# Configure Docker for GCR
echo -e "${YELLOW}🔧 Configuring Docker for Google Container Registry...${NC}"
gcloud auth configure-docker

# Build the Docker image
echo -e "${YELLOW}🏗️  Building Docker image...${NC}"
IMAGE_URL="gcr.io/$PROJECT_ID/legal-api:$TAG"
echo "Building: $IMAGE_URL"

docker build --platform linux/amd64 -t $IMAGE_URL .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully${NC}"
else
    echo -e "${RED}❌ Docker build failed${NC}"
    exit 1
fi

# Push the Docker image
echo -e "${YELLOW}📤 Pushing Docker image to GCR...${NC}"
docker push $IMAGE_URL

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image pushed successfully${NC}"
else
    echo -e "${RED}❌ Docker push failed${NC}"
    exit 1
fi

# Deploy to Cloud Run
echo -e "${YELLOW}🚀 Deploying to Cloud Run...${NC}"
echo "Service: $SERVICE_NAME"
echo "Region: $REGION"
echo "Image: $IMAGE_URL"

gcloud run deploy $SERVICE_NAME \
    --image $IMAGE_URL \
    --region $REGION \
    --memory 1Gi \
    --max-instances 3 \
    --no-allow-unauthenticated \
    --set-env-vars "CACHE_BACKEND=memory" \
    --set-env-vars "RATE_LIMIT_BACKEND=memory" \
    --set-env-vars "SUPABASE_JWKS_URL=$SUPABASE_JWKS_URL" \
    --set-env-vars "LOG_LEVEL=INFO" \
    --platform managed \
    --port 8000 \
    --timeout 300 \
    --concurrency 80

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Cloud Run deployment successful${NC}"
else
    echo -e "${RED}❌ Cloud Run deployment failed${NC}"
    exit 1
fi

# Get the service URL
echo -e "${YELLOW}🔍 Getting service URL...${NC}"
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format='value(status.url)')

if [ -z "$SERVICE_URL" ]; then
    echo -e "${RED}❌ Failed to get service URL${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Service deployed successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Deployment Summary${NC}"
echo "====================="
echo "🌐 Service URL: $SERVICE_URL"
echo "🏷️  Image: $IMAGE_URL"
echo "📍 Region: $REGION"
echo "🔧 Cache: In-memory (staging environment)"
echo "📊 Graph API: React-Flow compatible JSON"
echo "⚡ Authority Job: PageRank + recency boost"
echo ""

# Smoke test the deployment
echo -e "${YELLOW}🧪 Running smoke tests...${NC}"

# Test health endpoint
echo "Testing health endpoint..."
HEALTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/health")

if [ "$HEALTH_RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${RED}❌ Health check failed (HTTP $HEALTH_RESPONSE)${NC}"
fi

# Test API root
echo "Testing API root..."
ROOT_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/")

if [ "$ROOT_RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ API root accessible${NC}"
else
    echo -e "${RED}❌ API root failed (HTTP $ROOT_RESPONSE)${NC}"
fi

# Test sample graph endpoint (no auth required)
echo "Testing sample graph endpoint..."
SAMPLE_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/v0/graph/sample")

if [ "$SAMPLE_RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Sample graph endpoint working${NC}"
else
    echo -e "${RED}❌ Sample graph endpoint failed (HTTP $SAMPLE_RESPONSE)${NC}"
fi

# Test metrics endpoint
echo "Testing metrics endpoint..."
METRICS_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/metrics")

if [ "$METRICS_RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Metrics endpoint working${NC}"
else
    echo -e "${RED}❌ Metrics endpoint failed (HTTP $METRICS_RESPONSE)${NC}"
fi

echo ""
echo -e "${BLUE}🎯 Next Steps${NC}"
echo "============="
echo "1. Test authenticated endpoints with Supabase JWT:"
echo "   curl -H \"Authorization: Bearer <jwt_token>\" \\"
echo "        \"$SERVICE_URL/v0/search?q=test\" -i"
echo ""
echo "2. Test recommendation endpoint:"
echo "   curl -H \"Authorization: Bearer <jwt_token>\" \\"
echo "        \"$SERVICE_URL/v0/recommend/C-2023-TX-123\" -i"
echo ""
echo "3. Test graph endpoint with React-Flow JSON:"
echo "   curl -H \"Authorization: Bearer <jwt_token>\" \\"
echo "        \"$SERVICE_URL/v0/graph?id=C-2023-TX-123&depth=2&max_nodes=200\" -i"
echo ""
echo "4. Set up nightly authority job (Cloud Scheduler):"
echo "   See docs/nightly_authority_job.md for setup instructions"
echo ""
echo "5. Share with Frontend/LangGraph team:"
echo "   Service URL: $SERVICE_URL"
echo "   Auth: Bearer token from Supabase"
echo "   Endpoints: /v0/search, /v0/recommend, /v0/graph"
echo "   Graph Format: React-Flow compatible JSON"
echo ""
echo -e "${GREEN}🎉 Week 6 deployment complete!${NC}"
