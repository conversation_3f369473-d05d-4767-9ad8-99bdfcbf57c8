"""
Tests for Week 6 Graph API implementation.
Tests depth, truncation, schema validation, and performance.
"""

import pytest
import time
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient

from src.api.main import app
from src.api.graph.graph_service import GraphDataService, GraphResponse


class TestGraphAPI:
    """Test suite for Graph API endpoints."""
    
    def setup_method(self):
        """Setup test client and mocks."""
        self.client = TestClient(app)
        self.mock_user = {
            "user_id": "test-user-123",
            "jurisdictions": ["tx", "fed"],
            "permissions_hash": "test-hash-123"
        }
    
    @patch('src.api.graph.graph_router.get_current_user')
    def test_graph_basic_request(self, mock_auth):
        """Test basic graph API request with default parameters."""
        mock_auth.return_value = self.mock_user
        
        with patch.object(GraphDataService, 'get_graph_data') as mock_service:
            # Mock response
            mock_service.return_value = GraphResponse(
                nodes=[
                    {
                        "id": "C-2023-TX-123",
                        "label": "Test Case",
                        "type": "case",
                        "authority": 0.75,
                        "data": {"jurisdiction": "tx", "court": "Test Court"}
                    }
                ],
                edges=[],
                metadata={
                    "center_node": "C-2023-TX-123",
                    "returned_nodes": 1,
                    "returned_edges": 0,
                    "total_nodes": 1,
                    "total_edges": 0,
                    "truncated": False,
                    "query_time_ms": 50,
                    "depth_used": 2,
                    "direction_used": "both",
                    "max_nodes_requested": 200,
                    "max_edges_requested": 600
                }
            )
            
            response = self.client.get("/v0/graph?id=C-2023-TX-123")
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify response structure
            assert "nodes" in data
            assert "edges" in data
            assert "metadata" in data
            
            # Verify node structure
            assert len(data["nodes"]) == 1
            node = data["nodes"][0]
            assert node["id"] == "C-2023-TX-123"
            assert node["type"] == "case"
            assert "authority" in node
            assert "data" in node
    
    @patch('src.api.graph.graph_router.get_current_user')
    def test_graph_depth_parameter(self, mock_auth):
        """Test graph API with different depth parameters."""
        mock_auth.return_value = self.mock_user
        
        with patch.object(GraphDataService, 'get_graph_data') as mock_service:
            mock_service.return_value = GraphResponse(
                nodes=[], edges=[], 
                metadata={"depth_used": 3, "query_time_ms": 100}
            )
            
            # Test depth=3
            response = self.client.get("/v0/graph?id=test&depth=3")
            assert response.status_code == 200
            
            # Verify service was called with correct depth
            mock_service.assert_called_with(
                doc_id="test",
                depth=3,
                direction="both",
                max_nodes=None,
                max_edges=None,
                node_types=None,
                user_permissions_hash="test-hash-123"
            )
    
    @patch('src.api.graph.graph_router.get_current_user')
    def test_graph_truncation_path(self, mock_auth):
        """Test graph truncation with large datasets (hard caps 500/1500)."""
        mock_auth.return_value = self.mock_user
        
        with patch.object(GraphDataService, 'get_graph_data') as mock_service:
            # Mock truncated response
            mock_service.return_value = GraphResponse(
                nodes=[{"id": f"node-{i}", "label": f"Node {i}", "type": "case", 
                       "authority": 0.5, "data": {}} for i in range(500)],
                edges=[{"id": f"edge-{i}", "source": f"node-{i}", "target": f"node-{i+1}", 
                       "type": "cites", "data": {}} for i in range(1500)],
                metadata={
                    "center_node": "test-node",
                    "returned_nodes": 500,
                    "returned_edges": 1500,
                    "total_nodes": 2000,
                    "total_edges": 5000,
                    "truncated": True,
                    "query_time_ms": 450,
                    "depth_used": 3,
                    "direction_used": "both",
                    "max_nodes_requested": 500,
                    "max_edges_requested": 1500
                }
            )
            
            response = self.client.get("/v0/graph?id=test-node&depth=3&max_nodes=500&max_edges=1500")
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify truncation metadata
            assert data["metadata"]["truncated"] is True
            assert data["metadata"]["returned_nodes"] == 500
            assert data["metadata"]["returned_edges"] == 1500
            assert data["metadata"]["total_nodes"] == 2000
            assert data["metadata"]["total_edges"] == 5000
            
            # Verify hard caps are enforced
            assert len(data["nodes"]) == 500
            assert len(data["edges"]) == 1500
    
    @patch('src.api.graph.graph_router.get_current_user')
    def test_graph_json_schema(self, mock_auth):
        """Smoke test for React-Flow JSON schema compatibility."""
        mock_auth.return_value = self.mock_user
        
        with patch.object(GraphDataService, 'get_graph_data') as mock_service:
            # Mock realistic response
            mock_service.return_value = GraphResponse(
                nodes=[
                    {
                        "id": "C-2023-TX-123",
                        "label": "Smith v. Jones",
                        "type": "case",
                        "authority": 0.87,
                        "data": {
                            "jurisdiction": "tx",
                            "court": "Texas Supreme Court",
                            "date": "2023-03-15",
                            "practice_areas": ["personal_injury"]
                        }
                    },
                    {
                        "id": "S-TX-CPRC-74",
                        "label": "§74.001 CPRC",
                        "type": "statute",
                        "authority": 0.65,
                        "data": {
                            "jurisdiction": "tx",
                            "chapter": "74",
                            "section": "001"
                        }
                    }
                ],
                edges=[
                    {
                        "id": "edge-1",
                        "source": "C-2023-TX-123",
                        "target": "S-TX-CPRC-74",
                        "type": "cites",
                        "data": {
                            "weight": 12,
                            "relationship_type": "applies"
                        }
                    }
                ],
                metadata={
                    "center_node": "C-2023-TX-123",
                    "returned_nodes": 2,
                    "returned_edges": 1,
                    "total_nodes": 2,
                    "total_edges": 1,
                    "truncated": False,
                    "query_time_ms": 156
                }
            )
            
            response = self.client.get("/v0/graph?id=C-2023-TX-123")
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify React-Flow compatible structure
            assert "nodes" in data
            assert "edges" in data
            assert "metadata" in data
            
            # Verify nodes have required React-Flow keys
            required_node_keys = ["id", "label", "type", "authority", "data"]
            for node in data["nodes"]:
                for key in required_node_keys:
                    assert key in node, f"Missing required node key: {key}"
                
                # Verify authority is a float between 0 and 1
                assert isinstance(node["authority"], (int, float))
                assert 0 <= node["authority"] <= 1
            
            # Verify edges have required React-Flow keys
            required_edge_keys = ["id", "source", "target", "type", "data"]
            for edge in data["edges"]:
                for key in required_edge_keys:
                    assert key in edge, f"Missing required edge key: {key}"
                
                # Verify source and target reference existing nodes
                node_ids = [node["id"] for node in data["nodes"]]
                assert edge["source"] in node_ids
                assert edge["target"] in node_ids
            
            # Verify metadata structure
            required_metadata_keys = [
                "center_node", "returned_nodes", "returned_edges", 
                "total_nodes", "total_edges", "truncated", "query_time_ms"
            ]
            for key in required_metadata_keys:
                assert key in data["metadata"], f"Missing required metadata key: {key}"
    
    @patch('src.api.graph.graph_router.get_current_user')
    def test_graph_direction_parameter(self, mock_auth):
        """Test graph API with different direction parameters."""
        mock_auth.return_value = self.mock_user
        
        with patch.object(GraphDataService, 'get_graph_data') as mock_service:
            mock_service.return_value = GraphResponse(
                nodes=[], edges=[], metadata={"direction_used": "in"}
            )
            
            # Test direction=in
            response = self.client.get("/v0/graph?id=test&direction=in")
            assert response.status_code == 200
            
            # Verify service was called with correct direction
            args, kwargs = mock_service.call_args
            assert kwargs["direction"] == "in"
    
    @patch('src.api.graph.graph_router.get_current_user')
    def test_graph_node_type_filter(self, mock_auth):
        """Test graph API with node type filtering."""
        mock_auth.return_value = self.mock_user
        
        with patch.object(GraphDataService, 'get_graph_data') as mock_service:
            mock_service.return_value = GraphResponse(
                nodes=[], edges=[], metadata={}
            )
            
            # Test node_types filter
            response = self.client.get("/v0/graph?id=test&node_types=case&node_types=statute")
            assert response.status_code == 200
            
            # Verify service was called with correct node types
            args, kwargs = mock_service.call_args
            assert kwargs["node_types"] == ["case", "statute"]
    
    def test_graph_parameter_validation(self):
        """Test parameter validation for graph API."""
        # Test invalid depth
        response = self.client.get("/v0/graph?id=test&depth=5")
        assert response.status_code == 422
        
        # Test invalid direction
        response = self.client.get("/v0/graph?id=test&direction=invalid")
        assert response.status_code == 422
        
        # Test invalid max_nodes
        response = self.client.get("/v0/graph?id=test&max_nodes=1000")
        assert response.status_code == 422
        
        # Test invalid max_edges
        response = self.client.get("/v0/graph?id=test&max_edges=2000")
        assert response.status_code == 422
    
    def test_graph_sample_endpoint(self):
        """Test sample graph data endpoint."""
        response = self.client.get("/v0/graph/sample")
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify basic structure
        assert "nodes" in data
        assert "edges" in data
        assert "metadata" in data
        
        # Should have at least one sample node
        assert len(data["nodes"]) >= 1
    
    @patch('src.api.graph.graph_router.get_current_user')
    def test_graph_performance_target(self, mock_auth):
        """Test that graph API meets performance targets (<500ms)."""
        mock_auth.return_value = self.mock_user
        
        with patch.object(GraphDataService, 'get_graph_data') as mock_service:
            # Mock response with realistic timing
            mock_service.return_value = GraphResponse(
                nodes=[{"id": f"node-{i}", "label": f"Node {i}", "type": "case", 
                       "authority": 0.5, "data": {}} for i in range(200)],
                edges=[{"id": f"edge-{i}", "source": f"node-{i}", "target": f"node-{i+1}", 
                       "type": "cites", "data": {}} for i in range(600)],
                metadata={"query_time_ms": 450}  # Under 500ms target
            )
            
            start_time = time.time()
            response = self.client.get("/v0/graph?id=test&max_nodes=200&max_edges=600")
            end_time = time.time()
            
            assert response.status_code == 200
            
            # Verify response time is reasonable (allowing for test overhead)
            response_time_ms = (end_time - start_time) * 1000
            assert response_time_ms < 1000  # Allow 1 second for test environment
            
            # Verify reported query time meets target
            data = response.json()
            assert data["metadata"]["query_time_ms"] < 500
